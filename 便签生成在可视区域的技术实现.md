# 便签生成在可视区域的技术实现

在无限画布应用中，确保新生成的便签总是出现在用户当前可见的屏幕区域内是一个重要的用户体验问题。本文档详细说明了此功能的技术实现原理。

## 双坐标系统

应用使用了两套坐标系统：

1. **屏幕坐标系**：相对于浏览器视口的固定坐标，范围有限，就是用户能看到的区域
2. **画布坐标系**：相对于无限画布的绝对坐标，可以无限延伸

## 核心实现方法

应用在生成新便签时的关键步骤是：

### 1. 坐标系统转换方法

`Canvas.js`中定义了两个关键的坐标转换方法：

```javascript
// 屏幕坐标转换为画布坐标
screenToCanvasPosition(screenX, screenY) {
  const rect = this.canvas.getBoundingClientRect();
  const canvasX = (screenX - rect.left - this.offset.x) / this.scale;
  const canvasY = (screenY - rect.top - this.offset.y) / this.scale;
  return { x: canvasX, y: canvasY };
}

// 画布坐标转换为屏幕坐标
canvasToScreenPosition(canvasX, canvasY) {
  const rect = this.canvas.getBoundingClientRect();
  const screenX = canvasX * this.scale + this.offset.x + rect.left;
  const screenY = canvasY * this.scale + this.offset.y + rect.top;
  return { x: screenX, y: screenY };
}
```

### 2. 便签位置计算逻辑

在`TempNote.js`和`App.js`中的便签创建函数中，都使用了相同的位置计算逻辑：

```javascript
// 获取视口尺寸
const viewportWidth = window.innerWidth;
const viewportHeight = window.innerHeight;

// 获取当前已存在的便签数量
const existingNotes = document.querySelectorAll(".note").length;

// 计算屏幕居中偏左上的基准位置
const baseScreenX = Math.max(viewportWidth * 0.25, 80);
const baseScreenY = Math.max(viewportHeight * 0.15, 60);

// 根据已存在的便签数量计算错开位置
const offsetX = (existingNotes % 5) * 60; // 每个便签水平错开60像素
const offsetY = Math.floor(existingNotes / 5) * 80; // 每行5个，垂直错开80像素

// 计算最终的屏幕坐标，添加少量随机性使排列更自然
const screenX = baseScreenX + offsetX + (Math.random() * 20 - 10);
const screenY = baseScreenY + offsetY + (Math.random() * 20 - 10);

// 将屏幕坐标转换为画布坐标
const canvasPos = canvas.screenToCanvasPosition(screenX, screenY);
x = canvasPos.x;
y = canvasPos.y;
```

## 工作原理详解

这个实现的关键点在于：

1. **以视口为基准点计算**：
   - 应用首先计算当前视口中的一个合适位置（居中偏左上方）
   - 这确保了便签会出现在用户当前可见的屏幕区域内

2. **错开排列策略**：
   - 便签按照网格状排列，每行最多5个
   - 当一行放满后，自动换到下一行
   - 添加少量随机偏移使布局更自然，避免完全规则排列的呆板感

3. **坐标转换机制**：
   - 最关键的步骤是将计算好的屏幕坐标转换为画布坐标
   - 这样无论用户将画布缩放或平移到什么位置，新便签都会生成在当前视图中

4. **存储与显示**：
   - 便签的位置以画布坐标形式存储，使其在画布上有固定的位置
   - 渲染时会根据当前的画布变换自动计算出正确的显示位置

## 优势与巧妙之处

这种实现方式的优势在于：

1. **始终可见**：无论画布如何缩放或平移，新便签总是出现在用户当前视图中
2. **组织有序**：便签以网格状排列，避免相互重叠
3. **自然排布**：小的随机偏移使布局更自然，不会过于机械
4. **适应性强**：同时考虑了屏幕大小，在不同设备上都能合理放置便签

通过这种坐标系转换和位置计算的组合机制，应用巧妙地解决了在无限画布中定位新元素的问题，保证了良好的用户体验。

## 代码实现示例

### `TempNote.js` 中创建临时AI便签的代码：

```javascript
export function createEmptyAiNote() {
  // 计算屏幕可见区域内的位置 - 确保便签生成在屏幕内
  // 获取画布实例以便进行坐标转换
  const canvas = window.canvasInstance;

  // 默认位置（如果无法获取画布实例）
  let x = 100 + Math.random() * 200;
  let y = 100 + Math.random() * 200;

  if (canvas) {
    // 获取视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 获取当前已存在的便签数量，用于计算错开位置
    const existingNotes = document.querySelectorAll(".note").length;

    // 计算屏幕居中偏左上的基准位置
    const baseScreenX = Math.max(viewportWidth * 0.25, 80);
    const baseScreenY = Math.max(viewportHeight * 0.15, 60);

    // 根据已存在的便签数量计算错开位置
    // 使用错开模式，每个新便签都会在基准位置的基础上错开一定距离
    const offsetX = (existingNotes % 5) * 60; // 每个便签水平错开60像素
    const offsetY = Math.floor(existingNotes / 5) * 80; // 每行5个便签，每行垂直错开80像素

    // 计算最终的屏幕坐标，并添加少量随机性
    const screenX = baseScreenX + offsetX + (Math.random() * 20 - 10);
    const screenY = baseScreenY + offsetY + (Math.random() * 20 - 10);

    // 将屏幕坐标转换为画布坐标
    const canvasPos = canvas.screenToCanvasPosition(screenX, screenY);
    x = canvasPos.x;
    y = canvasPos.y;

    console.log("便签生成位置(画布坐标):", {
      x,
      y,
      existingNotes,
      offsetX,
      offsetY,
    });
  }

  // 后续代码：创建便签元素并设置位置...
}
```

### `App.js` 中添加空白便签的代码：

```javascript
async addEmptyNote() {
  try {
    // 计算屏幕可见区域内的位置 - 确保便签生成在屏幕内
    // 获取画布实例以便进行坐标转换
    const canvas = window.canvasInstance;

    // 默认位置（如果无法获取画布实例）
    let x = 100 + Math.random() * 200;
    let y = 100 + Math.random() * 200;

    if (canvas) {
      // 获取视口尺寸
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // 获取当前已存在的便签数量，用于计算错开位置
      const existingNotes = document.querySelectorAll(".note").length;

      // 计算屏幕居中偏左上的基准位置
      const baseScreenX = Math.max(viewportWidth * 0.25, 80);
      const baseScreenY = Math.max(viewportHeight * 0.15, 60);

      // 根据已存在的便签数量计算错开位置
      // 使用错开模式，每个新便签都会在基准位置的基础上错开一定距离
      const offsetX = (existingNotes % 5) * 60; // 每个便签水平错开60像素
      const offsetY = Math.floor(existingNotes / 5) * 80; // 每行5个便签，每行垂直错开80像素

      // 计算最终的屏幕坐标，并添加少量随机性
      const screenX = baseScreenX + offsetX + (Math.random() * 20 - 10);
      const screenY = baseScreenY + offsetY + (Math.random() * 20 - 10);

      // 将屏幕坐标转换为画布坐标
      const canvasPos = canvas.screenToCanvasPosition(screenX, screenY);
      x = canvasPos.x;
      y = canvasPos.y;

      console.log("空白便签生成位置(画布坐标):", {
        x,
        y,
        existingNotes,
        offsetX,
        offsetY,
      });
    }

    // 后续代码：创建便签元素并保存到服务器...
  } catch (error) {
    console.error("添加空白便签失败:", error);
    this.showMessage("添加便签失败", "error");
  }
}
```
